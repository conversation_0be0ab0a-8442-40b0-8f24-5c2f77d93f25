{"pagination": {"ListServers": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Servers"}, "ListAccesses": {"input_token": "NextToken", "limit_key": "MaxResults", "non_aggregate_keys": ["ServerId"], "output_token": "NextToken", "result_key": "Accesses"}, "ListExecutions": {"input_token": "NextToken", "limit_key": "MaxResults", "non_aggregate_keys": ["WorkflowId"], "output_token": "NextToken", "result_key": "Executions"}, "ListSecurityPolicies": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "SecurityPolicyNames"}, "ListTagsForResource": {"input_token": "NextToken", "limit_key": "MaxResults", "non_aggregate_keys": ["<PERSON><PERSON>"], "output_token": "NextToken", "result_key": "Tags"}, "ListUsers": {"input_token": "NextToken", "limit_key": "MaxResults", "non_aggregate_keys": ["ServerId"], "output_token": "NextToken", "result_key": "Users"}, "ListWorkflows": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Workflows"}, "ListAgreements": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Agreements"}, "ListCertificates": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Certificates"}, "ListConnectors": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Connectors"}, "ListProfiles": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Profiles"}, "ListFileTransferResults": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "FileTransferResults"}, "ListWebApps": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "WebApps"}}}