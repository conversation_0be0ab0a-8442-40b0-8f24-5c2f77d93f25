{"pagination": {"ListBootstrapActions": {"input_token": "<PERSON><PERSON>", "output_token": "<PERSON><PERSON>", "result_key": "BootstrapActions"}, "ListClusters": {"input_token": "<PERSON><PERSON>", "output_token": "<PERSON><PERSON>", "result_key": "Clusters"}, "ListInstanceGroups": {"input_token": "<PERSON><PERSON>", "output_token": "<PERSON><PERSON>", "result_key": "InstanceGroups"}, "ListInstances": {"input_token": "<PERSON><PERSON>", "output_token": "<PERSON><PERSON>", "result_key": "Instances"}, "ListSteps": {"input_token": "<PERSON><PERSON>", "output_token": "<PERSON><PERSON>", "result_key": "Steps"}, "ListInstanceFleets": {"input_token": "<PERSON><PERSON>", "output_token": "<PERSON><PERSON>", "result_key": "InstanceF<PERSON>ts"}, "ListSecurityConfigurations": {"input_token": "<PERSON><PERSON>", "output_token": "<PERSON><PERSON>", "result_key": "SecurityConfigurations"}, "ListNotebookExecutions": {"input_token": "<PERSON><PERSON>", "output_token": "<PERSON><PERSON>", "result_key": "NotebookExecutions"}, "ListStudioSessionMappings": {"input_token": "<PERSON><PERSON>", "output_token": "<PERSON><PERSON>", "result_key": "SessionMappings"}, "ListStudios": {"input_token": "<PERSON><PERSON>", "output_token": "<PERSON><PERSON>", "result_key": "Studios"}}}