#!/usr/bin/env python3
"""
AWS SSM Command Runner Script
This script runs commands on EC2 instances via AWS Systems Manager (SSM) using boto3
"""

import argparse
import sys
import time
import boto3
from botocore.exceptions import ClientError, NoCredentialsError, ProfileNotFound
from pick import pick


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Run commands on AWS EC2 instances via SSM"
    )
    parser.add_argument(
        "--profile", 
        default="lab",
        help="AWS profile to use (default: fedramp)"
    )
    parser.add_argument(
        "--region",
        default="us-gov-east-1", 
        help="AWS region to use (default: us-gov-east-1)"
    )
    parser.add_argument(
        "--timeout",
        type=int,
        default=1200,
        help="Command timeout in seconds (default: 1200)"
    )
    return parser.parse_args()


def create_aws_session(profile, region):
    """Create AWS session with specified profile and region"""
    try:
        session = boto3.Session(profile_name=profile, region_name=region)
        return session
    except ProfileNotFound:
        print(f"Error: AWS profile '{profile}' not found")
        sys.exit(1)
    except NoCredentialsError:
        print("Error: AWS credentials not found")
        sys.exit(1)


def get_ec2_instances(session):
    """Fetch EC2 instances from AWS"""
    try:
        ec2 = session.client('ec2')
        response = ec2.describe_instances()
        
        instances = []
        for reservation in response['Reservations']:
            for instance in reservation['Instances']:
                instances.append(instance)
        
        print(f"Found {len(instances)} total instances")
        return instances
    except ClientError as e:
        print(f"Error fetching instances: {e}")
        sys.exit(1)


def format_instance_for_menu(instance):
    """Format instance data for display in menu"""
    instance_id = instance['InstanceId']
    
    # Get instance name from tags
    name = "no-name"
    if 'Tags' in instance:
        for tag in instance['Tags']:
            if tag['Key'] == 'Name':
                name = tag['Value']
                break
    
    # Get instance state
    state = instance['State']['Name']
    
    # Get instance type
    instance_type = instance['InstanceType']
    
    # Format display string
    display = f"{instance_id} | {name}"
    
    return {
        'display': display,
        'instance_id': instance_id,
        'name': name,
        'state': state
    }


def filter_running_instances(instances):
    """Filter instances to only show running ones"""
    running_instances = []
    for instance in instances:
        if instance['State']['Name'] == 'running':
            running_instances.append(instance)
    return running_instances


def run_ssm_command(session, instance_id, commands, timeout_seconds):
    """Run commands on instance via SSM"""
    try:
        ssm = session.client('ssm')
        
        print(f"Sending commands to {instance_id}...")
        
        # Send command
        response = ssm.send_command(
            InstanceIds=[instance_id],
            DocumentName='AWS-RunShellScript',
            Parameters={
                'commands': commands,
                'executionTimeout': [str(timeout_seconds)]
            },
            TimeoutSeconds=timeout_seconds
        )
        
        command_id = response['Command']['CommandId']
        print(f"Command ID: {command_id}")
        
        # Wait for command to complete
        print("Waiting for command to complete...")
        waiter = ssm.get_waiter('command_executed')
        
        try:
            waiter.wait(
                CommandId=command_id,
                InstanceId=instance_id,
                WaiterConfig={
                    'Delay': 5,
                    'MaxAttempts': timeout_seconds // 5
                }
            )
        except Exception as e:
            print(f"Command may still be running or failed: {e}")
        
        # Get command output
        output_response = ssm.get_command_invocation(
            CommandId=command_id,
            InstanceId=instance_id
        )
        
        status = output_response['Status']
        stdout = output_response.get('StandardOutputContent', '')
        stderr = output_response.get('StandardErrorContent', '')
        
        print(f"\nCommand Status: {status}")
        
        if stdout:
            print(f"\nStandard Output:\n{stdout}")
        
        if stderr:
            print(f"\nStandard Error:\n{stderr}")
        
        return status == 'Success'
        
    except ClientError as e:
        print(f"Error running SSM command: {e}")
        return False


def main():
    """Main function"""
    args = parse_arguments()
    
    # Define the commands to run
    commands = [
        'bash -c \'URL=$(curl -s -H "X-Artifact-Token: 0d0c56b6-b3e4-4758-a144-a7e702075b95" https://share.axonius.com/api/files/0a13493b-abde-44e7-a14e-e145f1ea9143/download-url); [[ $URL =~ ^https?:// ]] && curl -o "axonius_Ax-OS-1.7.10_encrypted.sh" "$URL" || echo "Error: Invalid response: $URL"\'',
        'aws s3 mv axonius_Ax-OS-1.7.10_encrypted.sh s3://afs-share/os/1.7/1.7.10/install/axonius_Ax-OS-1.7.10_encrypted.sh'
    ]
    
    print(f"Fetching EC2 instances from {args.region} using profile {args.profile}...")
    
    # Create AWS session
    session = create_aws_session(args.profile, args.region)
    
    # Get instances
    instances = get_ec2_instances(session)
    
    if not instances:
        print("No EC2 instances found.")
        sys.exit(0)
    
    # Filter to only running instances
    running_instances = filter_running_instances(instances)
    print(f"Found {len(running_instances)} running instances")
    
    if not running_instances:
        print("No running EC2 instances found.")
        # Show all instances for debugging
        if instances:
            print("\nAll instances found (including non-running):")
            for instance in instances:
                state = instance['State']['Name']
                instance_id = instance['InstanceId']
                print(f"  {instance_id}: {state}")
        sys.exit(0)
    
    # Format instances for menu
    menu_items = []
    instance_map = {}
    
    for instance in running_instances:
        formatted = format_instance_for_menu(instance)
        menu_items.append(formatted['display'])
        instance_map[formatted['display']] = formatted['instance_id']
    
    # Sort menu items in ascending order by instance name (second field in display string)
    menu_items.sort(key=lambda x: x.split(' | ')[1].lower())
    
    # Add quit option to menu
    menu_items.append("i-Quit")
    
    # Show interactive menu in a loop
    while True:
        try:
            title = f"Select an EC2 instance to run commands on (Profile: {args.profile}, Region: {args.region}, Timeout: {args.timeout}s):"
            selected_option, selected_index = pick(menu_items, title, indicator='=>')
            
            # Check if user selected quit option
            if selected_option == "i-Quit":
                print("Exiting....")
                sys.exit(0)
            
            # Get the instance ID for the selected option
            selected_instance_id = instance_map[selected_option]
            
            print(f"\nRunning commands on {selected_instance_id}...")
            print("Commands to execute:")
            for i, cmd in enumerate(commands, 1):
                print(f"  {i}. {cmd}")
            print()
            
            # Run SSM commands
            success = run_ssm_command(session, selected_instance_id, commands, args.timeout)
            
            if success:
                print(f"\n✅ Commands completed successfully on {selected_instance_id}")
            else:
                print(f"\n❌ Commands failed on {selected_instance_id}")
            
            print("Returning to instance menu...\n")
            
        except KeyboardInterrupt:
            print("\nCancelled.")
            sys.exit(0)


if __name__ == "__main__":
    main()
