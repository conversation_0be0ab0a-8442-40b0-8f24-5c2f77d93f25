#!/usr/bin/env python3
"""
AWS SSM Command Runner Script
This script runs commands on EC2 instances via AWS Systems Manager (SSM) using boto3
"""

import argparse
import sys
import time
import boto3
from botocore.exceptions import ClientError, NoCredentialsError, ProfileNotFound
from pick import pick


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Run commands on AWS EC2 instances via SSM"
    )
    parser.add_argument(
        "--profile", 
        default="lab",
        help="AWS profile to use (default: lab)"
    )
    parser.add_argument(
        "--region",
        default="us-gov-west-1", 
        help="AWS region to use (default: us-gov-west-1)"
    )
    parser.add_argument(
        "--timeout",
        type=int,
        default=1200,
        help="Command timeout in seconds (default: 1200)"
    )
    return parser.parse_args()


def create_aws_session(profile, region):
    """Create AWS session with specified profile and region"""
    try:
        session = boto3.Session(profile_name=profile, region_name=region)
        return session
    except ProfileNotFound:
        print(f"Error: AWS profile '{profile}' not found")
        sys.exit(1)
    except NoCredentialsError:
        print("Error: AWS credentials not found")
        sys.exit(1)

def run_ssm_command(session, instance_id, commands, timeout_seconds):
    """Run commands on instance via SSM"""
    try:
        ssm = session.client('ssm')
        
        print(f"Sending commands to {instance_id}...")
        
        # Send command
        response = ssm.send_command(
            InstanceIds=[instance_id],
            DocumentName='AWS-RunShellScript',
            Parameters={
                'commands': commands,
                'executionTimeout': [str(timeout_seconds)]
            },
            TimeoutSeconds=timeout_seconds
        )
        
        command_id = response['Command']['CommandId']
        print(f"Command ID: {command_id}")
        
        # Wait for command to complete
        print("Waiting for command to complete...")
        waiter = ssm.get_waiter('command_executed')
        
        try:
            waiter.wait(
                CommandId=command_id,
                InstanceId=instance_id,
                WaiterConfig={
                    'Delay': 5,
                    'MaxAttempts': timeout_seconds // 5
                }
            )
        except Exception as e:
            print(f"Command may still be running or failed: {e}")
        
        # Get command output
        output_response = ssm.get_command_invocation(
            CommandId=command_id,
            InstanceId=instance_id
        )
        
        status = output_response['Status']
        stdout = output_response.get('StandardOutputContent', '')
        stderr = output_response.get('StandardErrorContent', '')
        
        print(f"\nCommand Status: {status}")
        
        if stdout:
            print(f"\nStandard Output:\n{stdout}")
        
        if stderr:
            print(f"\nStandard Error:\n{stderr}")
        
        return status == 'Success'
        
    except ClientError as e:
        print(f"Error running SSM command: {e}")
        return False


def main():
    """Main function"""
    args = parse_arguments()
    
    # Define the commands to run
    commands = [
        'ls -al'
    ]
    
    print(f"Fetching EC2 instances from {args.region} using profile {args.profile}...")
    
    # Create AWS session
    session = create_aws_session(args.profile, args.region)
    
    try:
        # Get the instance ID for the selected option
        selected_instance_id = 'i-07bcd2e0f23d49c66' #instance_map[selected_option]
        
        print(f"\nRunning commands on {selected_instance_id}...")
        print("Commands to execute:")
        for i, cmd in enumerate(commands, 1):
            print(f"  {i}. {cmd}")
        print()
        
        # Run SSM commands
        success = run_ssm_command(session, selected_instance_id, commands, args.timeout)
        
        if success:
            print(f"\n✅ Commands completed successfully on {selected_instance_id}")
        else:
            print(f"\n❌ Commands failed on {selected_instance_id}")
        
    except KeyboardInterrupt:
        print("\nCancelled.")
        sys.exit(0)

if __name__ == "__main__":
    main()
