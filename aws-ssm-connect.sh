#!/bin/zsh
# AWS SSM Session Manager Connection Script
# This script provides an interactive way to connect to EC2 instances via AWS Systems Manager (SSM)
# It lists available instances, allows selection via arrow keys, and starts an SSM session

# Exit on any error, undefined variables, or pipe failures for robust error handling
set -euo pipefail

# Set AWS profile and region
PROFILE="fedramp"
REGION="us-gov-east-1"

# Build AWS CLI command array with profile and region
aws_cli=(aws --profile "$PROFILE" --region "$REGION")

# Verify that jq (JSON processor) is installed - required for parsing AWS CLI output
command -v jq >/dev/null || { echo "jq is required (brew install jq)"; exit 2; }

echo "Fetching EC2 instances from $REGION using profile $PROFILE..."

# Get list of all EC2 instances
instances_json="$("${aws_cli[@]}" ec2 describe-instances \
  --query 'Reservations[].Instances[]' \
  --output json 2>/dev/null || { echo "Failed to fetch instances"; exit 1; })"

# Check if any instances were found
if [[ "$(jq 'length' <<<"$instances_json")" -eq 0 ]]; then
  echo "No EC2 instances found."
  exit 0
fi

# Process the JSON data to create a menu-friendly format
menu_data="$(jq -r '.[] | 
  "\(.InstanceId)|\(.Tags // [] | map(select(.Key=="Name")) | .[0].Value // "no-name")"' \
  <<<"$instances_json" | sort -t'|' -k2)"

# Convert to array using zsh syntax
lines=("${(@f)menu_data}")

# Check if we have instances
if [[ ${#lines[@]} -eq 0 ]]; then
  echo "No instances found."
  exit 0
fi

# Simple menu function using select
echo "Available EC2 instances:"
echo ""

# Create a simple numbered menu
PS3="Select an instance (enter number): "
select choice in "${lines[@]}" "Quit"; do
  if [[ "$choice" == "Quit" ]]; then
    # echo "Cancelled."
    exit 0
  elif [[ -n "$choice" ]]; then
    # Extract instance ID from the selected line
    instance_id="${choice%%|*}"
    
    # Start SSM session with the selected instance
    echo "Starting SSM session with $instance_id ..."
    exec "${aws_cli[@]}" ssm start-session --target "$instance_id"
  else
    echo "Invalid selection. Please try again."
  fi
done
