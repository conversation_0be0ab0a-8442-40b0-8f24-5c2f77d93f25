#!/usr/bin/env python3
"""
AWS SSM Session Manager Connection Script
This script provides an interactive way to connect to EC2 instances via AWS Systems Manager (SSM)
It lists available instances, allows selection via arrow keys, and starts an SSM session
"""

import argparse
import sys
import subprocess
import boto3
from botocore.exceptions import ClientError, NoCredentialsError, ProfileNotFound
from pick import pick


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Interactive AWS EC2 instance selector for SSM connections"
    )
    parser.add_argument(
        "--profile",
        default="lab",
        help="AWS profile to use (default: lab)"
    )
    parser.add_argument(
        "--region",
        default="us-gov-west-1",
        help="AWS region to use (default: us-gov-west-1)"
    )
    return parser.parse_args()


def create_aws_session(profile, region):
    """Create AWS session with specified profile and region"""
    try:
        session = boto3.Session(profile_name=profile, region_name=region)
        return session
    except ProfileNotFound:
        print(f"Error: AWS profile '{profile}' not found")
        sys.exit(1)
    except NoCredentialsError:
        print("Error: AWS credentials not found")
        sys.exit(1)


def get_ec2_instances(session):
    """Fetch EC2 instances from AWS"""
    try:
        ec2 = session.client('ec2')
        response = ec2.describe_instances()

        instances = []
        for reservation in response['Reservations']:
            for instance in reservation['Instances']:
                instances.append(instance)

        print(f"Found {len(instances)} total instances")
        return instances
    except ClientError as e:
        print(f"Error fetching instances: {e}")
        sys.exit(1)


def format_instance_for_menu(instance):
    """Format instance data for display in menu"""
    instance_id = instance['InstanceId']

    # Get instance name from tags
    name = "no-name"
    if 'Tags' in instance:
        for tag in instance['Tags']:
            if tag['Key'] == 'Name':
                name = tag['Value']
                break

    # Get instance state
    state = instance['State']['Name']

    # Get instance type
    instance_type = instance['InstanceType']

    return {
        'instance_id': instance_id,
        'name': name,
        'state': state,
        'instance_type': instance_type
    }


def format_instances_as_table(instances_data):
    """Format instances as a table with proper column alignment"""
    if not instances_data:
        return []

    # Calculate column widths
    id_width = max(len(item['instance_id']) for item in instances_data)
    name_width = max(len(item['name']) for item in instances_data)
    state_width = max(len(item['state']) for item in instances_data)
    type_width = max(len(item['instance_type']) for item in instances_data)

    # Ensure minimum widths for headers
    id_width = max(id_width, len("Instance ID"))
    name_width = max(name_width, len("Name"))
    state_width = max(state_width, len("State"))
    type_width = max(type_width, len("Type"))

    # Create formatted menu items
    menu_items = []
    instance_map = {}

    for item in instances_data:
        # Format each row with proper spacing
        formatted_display = f"{item['instance_id']:<{id_width}} │ {item['name']:<{name_width}} │ {item['state']:<{state_width}} │ {item['instance_type']:<{type_width}}"
        menu_items.append(formatted_display)
        instance_map[formatted_display] = item['instance_id']

    return menu_items, instance_map


def filter_running_instances(instances):
    """Filter instances to only show running ones"""
    running_instances = []
    for instance in instances:
        if instance['State']['Name'] == 'running':
            running_instances.append(instance)
    return running_instances


def start_ssm_session(instance_id, profile, region):
    """Start SSM session with the selected instance"""
    try:
        cmd = [
            'aws', 'ssm', 'start-session',
            '--target', instance_id,
            '--profile', profile,
            '--region', region
        ]
        
        print(f"Starting SSM session with {instance_id}...")
        subprocess.run(cmd, check=True)
        
    except subprocess.CalledProcessError as e:
        print(f"Error starting SSM session: {e}")
        sys.exit(1)
    except FileNotFoundError:
        print("Error: AWS CLI not found. Please install the AWS CLI.")
        sys.exit(1)


def main():
    """Main function"""
    args = parse_arguments()
    
    print(f"Fetching EC2 instances from {args.region} using profile {args.profile}...")
    
    # Create AWS session
    session = create_aws_session(args.profile, args.region)
    
    # Get instances
    instances = get_ec2_instances(session)
    
    if not instances:
        print("No EC2 instances found.")
        sys.exit(0)
    
    # Filter to only running instances
    running_instances = filter_running_instances(instances)
    print(f"Found {len(running_instances)} running instances")

    if not running_instances:
        print("No running EC2 instances found.")
        # Show all instances for debugging
        if instances:
            print("\nAll instances found (including non-running):")
            for instance in instances:
                state = instance['State']['Name']
                instance_id = instance['InstanceId']
                print(f"  {instance_id}: {state}")
        sys.exit(0)
    
    # Format instances for menu
    instances_data = []
    for instance in running_instances:
        formatted = format_instance_for_menu(instance)
        instances_data.append(formatted)

    # Sort instances by name
    instances_data.sort(key=lambda x: x['name'].lower())

    # Format as table
    menu_items, instance_map = format_instances_as_table(instances_data)

    # Add quit option to menu
    menu_items.append("i-Quit")

    # Show interactive menu in a loop
    while True:
        try:
            title = f"Select an EC2 instance to connect via SSM (Profile: {args.profile}, Region: {args.region}):"
            selected_option, selected_index = pick(menu_items, title, indicator='=>')

            # Check if user selected quit option
            if selected_option == "i-Quit":
                print("Exiting....")
                sys.exit(0)

            # Get the instance ID for the selected option
            selected_instance_id = instance_map[selected_option]

            # Start SSM session
            start_ssm_session(selected_instance_id, args.profile, args.region)

            # After SSM session ends, show a message and continue the loop
            print(f"\nSSM session with {selected_instance_id} ended.")
            print("Returning to instance menu...\n")

        except KeyboardInterrupt:
            print("\nCancelled.")
            sys.exit(0)


if __name__ == "__main__":
    main()
